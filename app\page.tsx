"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Stethoscope,
  GraduationCap,
  Brain,
  Trophy,
  Users,

  Shield,
  BarChart3,
  Clock,
  Star,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Heart,
  Award,
  BookOpen,
  Target,
  Globe,
  Phone,
  Mail,
  MapPin,
  Play,
  ChevronRight,
  TrendingUp,
  Calendar,
  UserCheck,
  Lightbulb,
  Activity
} from "lucide-react"
import Link from "next/link"
import { motion, useScroll, useTransform } from "framer-motion"
import { useSession } from "next-auth/react"
import { useInView } from "react-intersection-observer"
import { TypeAnimation } from "react-type-animation"
import { useState, useEffect } from "react"

export default function NursingCoachingLandingPage() {
  const { data: session } = useSession()
  const { scrollY } = useScroll()
  const y1 = useTransform(scrollY, [0, 300], [0, 50])
  const y2 = useTransform(scrollY, [0, 300], [0, -50])

  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const nursingExams = [
    "NCLEX-RN",
    "NCLEX-PN",
    "CGFNS",
    "HAAD",
    "DHA",
    "AIIMS",
    "NEET-PG",
    "PROMETRIC"
  ]

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Learning",
      description: "Advanced AI algorithms create personalized study plans and adaptive quizzes based on your performance.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Activity,
      title: "Live Mock Tests",
      description: "Real-time mock exams that simulate actual nursing certification tests with instant feedback.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: BarChart3,
      title: "Smart Analytics",
      description: "Detailed performance analytics with weakness identification and improvement recommendations.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Users,
      title: "Expert Mentorship",
      description: "Connect with experienced nursing professionals and certified instructors for guidance.",
      color: "from-orange-500 to-red-500"
    },
    {
      icon: Clock,
      title: "Flexible Scheduling",
      description: "Study at your own pace with 24/7 access to courses and personalized study schedules.",
      color: "from-indigo-500 to-purple-500"
    },
    {
      icon: Globe,
      title: "Global Recognition",
      description: "Prepare for nursing exams recognized worldwide with our comprehensive curriculum.",
      color: "from-teal-500 to-blue-500"
    }
  ]

  const stats = [
    { number: "15K+", label: "Nursing Students" },
    { number: "95%", label: "Pass Rate" },
    { number: "50+", label: "Countries Served" },
    { number: "24/7", label: "Support Available" }
  ]

  const testimonials = [
    {
      name: "Sarah Mitchell, RN",
      role: "NCLEX-RN Graduate",
      content: "The AI-powered study plans helped me identify my weak areas and focus my preparation. I passed NCLEX-RN on my first attempt!",
      rating: 5,
      image: "/api/placeholder/60/60",
      exam: "NCLEX-RN"
    },
    {
      name: "Ahmed Hassan",
      role: "DHA License Holder",
      content: "The mock tests were incredibly similar to the actual DHA exam. The detailed explanations helped me understand concepts better.",
      rating: 5,
      image: "/api/placeholder/60/60",
      exam: "DHA"
    },
    {
      name: "Priya Sharma",
      role: "AIIMS Nursing Graduate",
      content: "The flexible scheduling allowed me to balance work and study. The mentorship program was invaluable for my success.",
      rating: 5,
      image: "/api/placeholder/60/60",
      exam: "AIIMS"
    },
    {
      name: "Maria Rodriguez",
      role: "CGFNS Certified",
      content: "Comprehensive preparation materials and expert guidance made my CGFNS journey smooth and successful.",
      rating: 5,
      image: "/api/placeholder/60/60",
      exam: "CGFNS"
    }
  ]

  const courses = [
    {
      title: "NCLEX-RN Mastery",
      description: "Comprehensive preparation for the National Council Licensure Examination for Registered Nurses",
      duration: "12 weeks",
      students: "5,200+",
      rating: 4.9,
      price: "$299",
      features: ["3,000+ Practice Questions", "Live Mock Tests", "AI Performance Analysis", "Expert Mentorship"]
    },
    {
      title: "DHA/HAAD Excellence",
      description: "Complete preparation for Dubai and Abu Dhabi Health Authority nursing examinations",
      duration: "10 weeks",
      students: "3,800+",
      rating: 4.8,
      price: "$249",
      features: ["2,500+ Practice Questions", "UAE-Specific Content", "Mock Interviews", "Job Placement Support"]
    },
    {
      title: "CGFNS Success Program",
      description: "Targeted preparation for Commission on Graduates of Foreign Nursing Schools certification",
      duration: "8 weeks",
      students: "2,100+",
      rating: 4.9,
      price: "$199",
      features: ["Qualifying Exam Prep", "Credentials Review", "English Proficiency", "Visa Screen Support"]
    }
  ]

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [testimonials.length])

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            style={{ y: y1 }}
            className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-teal-200 to-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"
          />
          <motion.div
            style={{ y: y2 }}
            className="absolute top-40 right-10 w-72 h-72 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"
          />
          <motion.div
            style={{ y: y1 }}
            className="absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Badge variant="outline" className="mb-6 px-6 py-3 text-sm font-medium border-teal-200 text-teal-700 bg-teal-50">
                <Stethoscope className="w-4 h-4 mr-2" />
                Next-Gen Nursing Education
              </Badge>

              <h1 className="text-5xl sm:text-7xl font-bold mb-6">
                <span className="bg-gradient-to-r from-teal-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Empowering the Future of
                </span>
                <br />
                <span className="text-6xl sm:text-8xl bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                  Nursing Education
                </span>
              </h1>

              <div className="text-2xl sm:text-3xl font-semibold text-gray-700 dark:text-gray-300 mb-8 h-16">
                <TypeAnimation
                  sequence={[
                    'NCLEX-RN Success', 2000,
                    'DHA Certification', 2000,
                    'HAAD Excellence', 2000,
                    'CGFNS Mastery', 2000,
                    'AIIMS Preparation', 2000,
                    'Global Recognition', 2000,
                  ]}
                  wrapper="span"
                  speed={50}
                  repeat={Infinity}
                  className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500"
                />
              </div>

              <p className="text-xl text-muted-foreground mb-10 max-w-4xl mx-auto leading-relaxed">
                Transform your nursing career with AI-powered learning, expert mentorship, and comprehensive exam preparation.
                Join thousands of successful nurses who achieved their dreams with our innovative coaching platform.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-6 justify-center mb-12"
            >
              <Button size="lg" className="h-16 px-10 text-lg bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300">
                <Play className="mr-3 h-6 w-6" />
                Join Free Demo
                <ArrowRight className="ml-3 h-6 w-6" />
              </Button>
              <Button variant="outline" size="lg" className="h-16 px-10 text-lg border-2 border-teal-200 hover:bg-teal-50 transition-all duration-300">
                <Calendar className="mr-3 h-6 w-6" />
                Book Strategy Call
              </Button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="text-4xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">{stat.number}</div>
                  <div className="text-sm text-muted-foreground font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* Exam Types Showcase */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto"
            >
              {nursingExams.map((exam, index) => (
                <motion.div
                  key={exam}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  whileHover={{ scale: 1.1, y: -5 }}
                  className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-full border border-teal-200 shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <span className="text-sm font-semibold text-teal-700">{exam}</span>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-24 bg-gradient-to-r from-teal-50 to-blue-50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-teal-200 text-teal-700 bg-teal-50">
                <Heart className="w-4 h-4 mr-2" />
                About Our Mission
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                Revolutionizing Nursing Education with AI
              </h2>
              <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                We combine cutting-edge artificial intelligence with proven educational methodologies to create
                the most effective nursing exam preparation platform. Our hybrid learning model adapts to your
                unique learning style and pace.
              </p>
              <div className="space-y-4">
                {[
                  "AI-powered personalized learning paths",
                  "Smart analytics for performance optimization",
                  "Expert-designed curriculum by certified nurses",
                  "24/7 global support and mentorship"
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3"
                  >
                    <CheckCircle className="w-6 h-6 text-teal-500 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{item}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-teal-100 to-blue-100 rounded-2xl p-8 shadow-2xl">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full flex items-center justify-center">
                  <GraduationCap className="w-12 h-12 text-white" />
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-teal-600">95%</div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">50+</div>
                    <div className="text-sm text-gray-600">Countries</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">15K+</div>
                    <div className="text-sm text-gray-600">Students</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">24/7</div>
                    <div className="text-sm text-gray-600">Support</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-blue-200 text-blue-700 bg-blue-50">
                <Sparkles className="w-4 h-4 mr-2" />
                Why Choose Us
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                Advanced Features for Nursing Success
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Experience the future of nursing education with our comprehensive suite of AI-powered tools
                and expert-designed learning modules.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-500"
                       style={{ background: `linear-gradient(135deg, ${feature.color.split(' ')[1]}, ${feature.color.split(' ')[3]})` }} />
                  <CardHeader className="relative z-10">
                    <motion.div
                      className={`w-16 h-16 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 shadow-lg`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    <CardTitle className="text-xl font-bold mb-3">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="relative z-10">
                    <CardDescription className="text-base leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section className="py-24 bg-gradient-to-br from-blue-50 to-teal-50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-purple-200 text-purple-700 bg-purple-50">
                <BookOpen className="w-4 h-4 mr-2" />
                Our Courses
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Comprehensive Nursing Programs
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Choose from our expertly designed courses tailored for different nursing examinations
                and career paths worldwide.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {courses.map((course, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full hover:shadow-2xl transition-all duration-500 border-0 bg-white dark:bg-gray-800 overflow-hidden relative">
                  <div className="absolute top-0 right-0 bg-gradient-to-l from-teal-500 to-blue-500 text-white px-4 py-2 rounded-bl-lg">
                    <span className="text-sm font-bold">{course.price}</span>
                  </div>

                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between mb-4">
                      <Badge variant="secondary" className="bg-teal-100 text-teal-700">
                        {course.duration}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium">{course.rating}</span>
                      </div>
                    </div>
                    <CardTitle className="text-2xl font-bold mb-3">{course.title}</CardTitle>
                    <CardDescription className="text-base leading-relaxed">
                      {course.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Users className="w-4 h-4 mr-2" />
                        {course.students} enrolled
                      </span>
                    </div>

                    <div className="space-y-3">
                      {course.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-teal-500 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Button className="w-full mt-6 bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 transition-all duration-300">
                      Enroll Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-green-200 text-green-700 bg-green-50">
                <Target className="w-4 h-4 mr-2" />
                How It Works
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                Your Path to Nursing Success
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Our proven 4-step methodology has helped thousands of nursing students achieve their career goals.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {[
              {
                step: "1",
                title: "Assessment & Planning",
                description: "Take our comprehensive assessment to identify your strengths and create a personalized study plan.",
                icon: Target,
                color: "from-blue-500 to-cyan-500"
              },
              {
                step: "2",
                title: "AI-Powered Learning",
                description: "Engage with adaptive content that adjusts to your learning pace and style using advanced AI.",
                icon: Brain,
                color: "from-purple-500 to-pink-500"
              },
              {
                step: "3",
                title: "Practice & Mock Tests",
                description: "Take unlimited practice tests and mock exams that simulate real nursing certification tests.",
                icon: Activity,
                color: "from-green-500 to-emerald-500"
              },
              {
                step: "4",
                title: "Expert Mentorship",
                description: "Get guidance from certified nursing professionals and track your progress with detailed analytics.",
                icon: UserCheck,
                color: "from-orange-500 to-red-500"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <motion.div
                  className={`w-20 h-20 bg-gradient-to-r ${item.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <item.icon className="w-10 h-10 text-white" />
                </motion.div>
                <div className="text-sm font-bold text-teal-600 mb-2">STEP {item.step}</div>
                <h3 className="text-xl font-bold mb-4">{item.title}</h3>
                <p className="text-muted-foreground leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Carousel Section */}
      <section className="py-24 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl" />
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-pink-200 text-pink-700 bg-pink-50">
                <Heart className="w-4 h-4 mr-2" />
                Success Stories
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                What Our Students Say
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Join thousands of successful nursing professionals who achieved their dreams with our platform.
              </p>
            </motion.div>
          </div>

          {/* Testimonials Carousel */}
          <div className="max-w-4xl mx-auto">
            <motion.div
              key={currentTestimonial}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <Card className="border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-2xl">
                <CardContent className="p-12">
                  <div className="flex justify-center mb-6">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="w-6 h-6 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>

                  <blockquote className="text-2xl font-medium text-gray-700 dark:text-gray-300 mb-8 leading-relaxed">
                    "{testimonials[currentTestimonial].content}"
                  </blockquote>

                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">
                        {testimonials[currentTestimonial].name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className="text-left">
                      <div className="font-bold text-lg">{testimonials[currentTestimonial].name}</div>
                      <div className="text-muted-foreground">{testimonials[currentTestimonial].role}</div>
                      <Badge variant="secondary" className="mt-1 bg-teal-100 text-teal-700">
                        {testimonials[currentTestimonial].exam}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Carousel Controls */}
            <div className="flex justify-center mt-8 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? 'bg-gradient-to-r from-teal-500 to-blue-500 w-8'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Additional Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group cursor-pointer"
                onClick={() => setCurrentTestimonial(index)}
              >
                <Card className="h-full border-0 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-gray-800/90 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-sm">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <div className="font-semibold text-sm">{testimonial.name.split(',')[0]}</div>
                        <Badge variant="secondary" className="text-xs bg-teal-100 text-teal-700">
                          {testimonial.exam}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      "{testimonial.content}"
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section with Parallax */}
      <section className="py-24 relative overflow-hidden">
        {/* Parallax Background */}
        <motion.div
          style={{ y: y1 }}
          className="absolute inset-0 bg-gradient-to-br from-teal-600 via-blue-600 to-purple-600"
        />
        <div className="absolute inset-0 bg-black/20" />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center text-white"
          >
            <Badge variant="outline" className="mb-6 px-6 py-3 text-sm font-medium border-white/30 text-white bg-white/10 backdrop-blur-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              Ready to Start Your Journey?
            </Badge>

            <h2 className="text-4xl sm:text-6xl font-bold mb-6">
              Book Your Free Strategy Call
            </h2>
            <p className="text-xl mb-10 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Get personalized guidance from our nursing education experts. Discover the best path
              to achieve your nursing certification goals with our AI-powered platform.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button size="lg" className="h-16 px-10 text-lg bg-white text-teal-600 hover:bg-gray-100 shadow-2xl">
                  <Calendar className="mr-3 h-6 w-6" />
                  Book Free Strategy Call
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="outline" size="lg" className="h-16 px-10 text-lg border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm">
                  <Play className="mr-3 h-6 w-6" />
                  Watch Demo
                </Button>
              </motion.div>
            </div>

            {/* Pulse Animation */}
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="mt-12 inline-block"
            >
              <div className="w-4 h-4 bg-white rounded-full opacity-75" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-24 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-teal-200 text-teal-700 bg-teal-50">
                <Mail className="w-4 h-4 mr-2" />
                Get In Touch
              </Badge>
              <h2 className="text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                Start Your Nursing Journey Today
              </h2>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                Ready to take the next step in your nursing career? Our expert team is here to guide you
                through every step of your certification journey.
              </p>

              <div className="space-y-6">
                {[
                  { icon: Phone, title: "Call Us", content: "+****************", subtext: "Available 24/7" },
                  { icon: Mail, title: "Email Us", content: "<EMAIL>", subtext: "Quick response guaranteed" },
                  { icon: MapPin, title: "Global Reach", content: "50+ Countries Served", subtext: "Worldwide support" }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-4"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-teal-500 to-blue-500 rounded-lg flex items-center justify-center">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-lg">{item.title}</div>
                      <div className="text-teal-600 font-medium">{item.content}</div>
                      <div className="text-sm text-muted-foreground">{item.subtext}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 shadow-2xl bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-2xl font-bold text-center">Get Your Free Consultation</CardTitle>
                  <CardDescription className="text-center">
                    Fill out the form below and we'll get back to you within 24 hours.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">First Name</label>
                      <Input placeholder="Enter your first name" className="h-12" />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Last Name</label>
                      <Input placeholder="Enter your last name" className="h-12" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email Address</label>
                    <Input type="email" placeholder="Enter your email" className="h-12" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Phone Number</label>
                    <Input type="tel" placeholder="Enter your phone number" className="h-12" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Exam</label>
                    <select className="w-full h-12 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500">
                      <option>Select your target exam</option>
                      {nursingExams.map(exam => (
                        <option key={exam} value={exam}>{exam}</option>
                      ))}
                    </select>
                  </div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button className="w-full h-12 bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-lg font-semibold">
                      Get Free Consultation
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <Stethoscope className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold">NursingCoach</span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Empowering the next generation of nursing professionals with AI-powered education and expert mentorship.
              </p>
              <div className="flex space-x-4">
                {['facebook', 'twitter', 'linkedin', 'instagram'].map((social) => (
                  <motion.div
                    key={social}
                    whileHover={{ scale: 1.2, y: -2 }}
                    className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center cursor-pointer hover:bg-teal-600 transition-colors duration-300"
                  >
                    <div className="w-5 h-5 bg-gray-400" />
                  </motion.div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-6">Courses</h3>
              <ul className="space-y-3">
                {nursingExams.slice(0, 6).map((exam) => (
                  <li key={exam}>
                    <a href="#" className="text-gray-400 hover:text-teal-400 transition-colors duration-300">
                      {exam} Preparation
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-6">Resources</h3>
              <ul className="space-y-3">
                {['Study Guides', 'Practice Tests', 'Mock Exams', 'Video Lectures', 'Expert Tips', 'Success Stories'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-teal-400 transition-colors duration-300">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-6">Support</h3>
              <ul className="space-y-3">
                {['Help Center', 'Contact Us', 'Live Chat', 'FAQ', 'Privacy Policy', 'Terms of Service'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-teal-400 transition-colors duration-300">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center">
            <p className="text-gray-400">
              © 2024 NursingCoach Institute. All rights reserved. | Empowering nurses worldwide.
            </p>
          </div>
        </div>

        {/* Scroll to Top Button */}
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 w-12 h-12 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 z-50"
        >
          <ChevronRight className="w-6 h-6 text-white transform -rotate-90" />
        </motion.button>
      </footer>
    </div>
  )
}
